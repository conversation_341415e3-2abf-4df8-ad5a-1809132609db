#!/usr/bin/env python3
"""
测试Jinja2翻译函数注入修复
"""

import requests
import time


def test_template_rendering():
    """测试模板渲染是否修复"""
    print("🧪 测试Jinja2翻译函数修复")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    languages = ["en", "zh", "fr"]
    
    for lang in languages:
        print(f"\n测试语言: {lang}")
        
        try:
            url = f"{base_url}/template-test"
            headers = {"Accept-Language": lang}
            
            response = requests.get(url, headers=headers, timeout=10)
            
            print(f"  状态码: {response.status_code}")
            print(f"  内容类型: {response.headers.get('content-type', 'N/A')}")
            print(f"  响应大小: {len(response.text)} 字符")
            
            # 检查是否还有未定义错误
            if "UndefinedError" in response.text:
                print("  ❌ 仍然存在UndefinedError")
                # 显示错误详情
                if len(response.text) < 1000:
                    print(f"  错误内容: {response.text[:500]}...")
            elif "'_' is undefined" in response.text:
                print("  ❌ 翻译函数仍未定义")
            elif response.status_code == 500:
                print("  ❌ 服务器内部错误")
                print(f"  响应内容: {response.text[:200]}...")
            elif response.status_code == 200:
                print("  ✅ 模板渲染成功")
                
                # 检查是否包含翻译内容
                if "VerificationCode" in response.text:
                    print("  ✅ 包含验证码相关内容")
                    
                    # 检查是否有实际的翻译（不是原始msgid）
                    if "_('VerificationCode_Title')" not in response.text:
                        print("  ✅ 翻译函数正常工作")
                    else:
                        print("  ⚠️ 翻译函数可能未正常工作，仍显示原始msgid")
                else:
                    print("  ⚠️ 未找到预期内容")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("  ❌ 服务器连接失败")
            print("  💡 请确保服务器正在运行: uvicorn bethune.app:app --reload")
            break
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print("💡 如果测试通过，说明Jinja2翻译函数注入已修复")
    print("💡 如果仍有问题，请检查服务器日志")


def test_health_check():
    """健康检查"""
    print("🏥 服务器健康检查")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
            return False
    except:
        print("❌ 服务器无法访问")
        return False


def test_simple_endpoint():
    """测试简单端点"""
    print("\n🧪 测试简单端点")
    
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        print(f"  /docs 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("  ✅ 基础路由正常")
            return True
        else:
            print("  ⚠️ 基础路由异常")
            return False
    except Exception as e:
        print(f"  ❌ 基础路由测试失败: {e}")
        return False


def main():
    print("🔧 Jinja2翻译函数修复验证")
    print("=" * 50)
    
    # 健康检查
    if not test_health_check():
        print("\n💡 请先启动服务器:")
        print("   uvicorn bethune.app:app --reload")
        return
    
    # 测试简单端点
    test_simple_endpoint()
    
    # 等待一下让服务器完全启动
    time.sleep(1)
    
    # 测试模板渲染
    test_template_rendering()


if __name__ == "__main__":
    main()
