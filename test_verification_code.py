#!/usr/bin/env python3
"""
测试验证码发送接口
"""

import requests
import json


def test_verification_code_api():
    """测试验证码发送API"""
    print("🧪 测试验证码发送接口")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    api_url = f"{base_url}/api/v1/auth/verification_code"
    
    # 测试数据
    test_data = {
        "email": "<EMAIL>"
    }
    
    languages = ["en", "zh", "fr"]
    
    for lang in languages:
        print(f"\n测试语言: {lang}")
        
        try:
            headers = {
                "Accept-Language": lang,
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                api_url, 
                headers=headers, 
                data=json.dumps(test_data),
                timeout=10
            )
            
            print(f"  状态码: {response.status_code}")
            print(f"  内容类型: {response.headers.get('content-type', 'N/A')}")
            
            if response.status_code == 200:
                print("  ✅ API调用成功")
                try:
                    response_data = response.json()
                    print(f"  响应数据: {response_data}")
                except:
                    print("  响应不是JSON格式")
            elif response.status_code == 500:
                print("  ❌ 服务器内部错误")
                print(f"  错误详情: {response.text[:200]}...")
                
                # 检查是否是翻译函数错误
                if "UndefinedError" in response.text:
                    print("  🔥 仍然存在翻译函数未定义错误")
                elif "'_' is undefined" in response.text:
                    print("  🔥 翻译函数'_'仍未定义")
            else:
                print(f"  ⚠️ 其他状态码: {response.status_code}")
                print(f"  响应内容: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            print("  ❌ 服务器连接失败")
            print("  💡 请确保服务器正在运行: uvicorn bethune.app:app --reload")
            break
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print("💡 如果API调用成功，说明邮件模板翻译问题已解决")


def test_template_endpoint():
    """测试模板端点"""
    print("\n🧪 测试模板端点")
    print("=" * 30)
    
    base_url = "http://localhost:8000"
    template_url = f"{base_url}/template-test"
    
    try:
        headers = {"Accept-Language": "zh"}
        response = requests.get(template_url, headers=headers, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 模板渲染成功")
        elif "UndefinedError" in response.text:
            print("❌ 模板仍有翻译函数错误")
        else:
            print(f"⚠️ 其他问题: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 模板测试失败: {e}")


def test_health_check():
    """健康检查"""
    print("🏥 服务器健康检查")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
            return False
    except:
        print("❌ 服务器无法访问")
        return False


def main():
    print("📧 验证码发送接口测试")
    print("=" * 50)
    
    # 健康检查
    if not test_health_check():
        print("\n💡 请先启动服务器:")
        print("   uvicorn bethune.app:app --reload")
        return
    
    # 测试模板端点
    test_template_endpoint()
    
    # 测试验证码API
    test_verification_code_api()
    
    print("\n📋 测试说明:")
    print("- 如果验证码API返回500错误且包含UndefinedError，说明邮件模板翻译函数仍未修复")
    print("- 如果API调用成功，说明问题已解决")
    print("- 模板端点测试用于对比Web模板和邮件模板的差异")


if __name__ == "__main__":
    main()
