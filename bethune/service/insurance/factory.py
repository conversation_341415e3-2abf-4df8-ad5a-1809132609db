from .broker import BrokerService
from .brokerage import BrokerageService
from .customer import CustomerService
from .insurance_application import InsuranceApplicationService
from .insurance_company import InsuranceCompanyService
from .insurance_consultation import InsuranceConsultationService
from .insurance_policy import InsurancePolicyService
from .lead import LeadService
from .promotion_material import PromotionMaterialService
from bethune.repository.insurance.factory import InsuranceRepositoryFactory
from bethune.service.insurance.insurance_consultation_application import InsuranceConsultationApplicationService
from bethune.service.insurance.insurance_consultation_customer import InsuranceConsultationCustomerService
from bethune.service.insurance.user_feedback import UserFeedbackService


class ServiceFactory:

    @staticmethod
    def create_insurance_company_service() -> InsuranceCompanyService:
        return InsuranceCompanyService(InsuranceRepositoryFactory.create_insurance_company_repository())

    @staticmethod
    def create_customer_service() -> CustomerService:
        return CustomerService(
            InsuranceRepositoryFactory.create_customer_repository(),
            InsuranceRepositoryFactory.create_insurance_application_repository(),
        )

    @staticmethod
    def create_insurance_application_service() -> InsuranceApplicationService:
        return InsuranceApplicationService(
            InsuranceRepositoryFactory.create_insurance_application_repository(),
            InsuranceRepositoryFactory.create_lead_repository(),
        )

    @staticmethod
    def create_broker_service() -> BrokerService:
        return BrokerService(
            InsuranceRepositoryFactory.create_broker_repository(),
            InsuranceRepositoryFactory.create_brokerage_repository(),
        )

    @staticmethod
    def create_lead_service() -> LeadService:
        return LeadService(InsuranceRepositoryFactory.create_lead_repository())

    @staticmethod
    def create_insurance_policy_service() -> InsurancePolicyService:
        return InsurancePolicyService(InsuranceRepositoryFactory.create_insurance_policy_repository())

    @staticmethod
    def create_user_feedback_service() -> UserFeedbackService:
        return UserFeedbackService(InsuranceRepositoryFactory.create_user_feedback_repository())

    @staticmethod
    def create_brokerage_service() -> BrokerageService:
        return BrokerageService(InsuranceRepositoryFactory.create_brokerage_repository())

    @staticmethod
    def create_insurance_consultation_service() -> InsuranceConsultationService:
        return InsuranceConsultationService(InsuranceRepositoryFactory.create_insurance_consultation_repository())

    @staticmethod
    def create_promotion_service() -> PromotionMaterialService:
        return PromotionMaterialService(InsuranceRepositoryFactory.create_promotion_material_repository())

    @staticmethod
    def create_insurance_consultation_application_service() -> InsuranceConsultationApplicationService:
        return InsuranceConsultationApplicationService(
            InsuranceRepositoryFactory.create_insurance_consultation_application_repository()
        )

    @staticmethod
    def create_insurance_consultation_customer_service() -> InsuranceConsultationCustomerService:
        return InsuranceConsultationCustomerService(
            InsuranceRepositoryFactory.create_insurance_consultation_customer_repository()
        )
