import logging
from contextvars import ContextVar
from typing import Optional, Callable, Any

from fastapi import Request, Response
from starlette.templating import Jinja2Templates
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

from fastapi_babel import Babel, BabelMiddleware
from fastapi_babel.local_context import context_var
from fastapi_babel.properties import RootConfigs

context_var: ContextVar[Babel] = ContextVar("gettext")

_global_jinja_templates: Optional[Jinja2Templates] = None

def set_global_jinja_templates(templates: Jinja2Templates) -> None:
    global _global_jinja_templates
    _global_jinja_templates = templates

    if templates:
        templates.env.globals['_'] = get_text

def get_text(raw_message) -> str:
    """
    获取原始消息的本地化文本
    :param raw_message 原始消息
    :return 本地化消息
    """
    try:
        return context_var.get()(raw_message)
    except LookupError:
        logging.warning(f"Can't find message: {raw_message}")
        return raw_message

# CustomBabelMiddleware 已被 DirectBabelMiddleware 替代
# 保留此注释作为历史记录，说明为什么需要自定义实现：
# 1. 原版 BabelMiddleware 的 babel.install_jinja() 方法不稳定
# 2. 依赖 fastapi-babel 内部 API，兼容性差
# 3. 语言验证逻辑复杂，不够灵活
#
# class CustomBabelMiddleware(BabelMiddleware):
#     # 已废弃，使用 DirectBabelMiddleware 替代


class DirectBabelMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, babel_configs: RootConfigs, locale_selector: Callable, jinja2_templates=None):
        super().__init__(app)
        self.babel_configs = babel_configs
        self.locale_selector = locale_selector
        self.jinja2_templates = jinja2_templates

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        lang_code: Optional[str] = self.locale_selector(request)
        babel = Babel(configs=self.babel_configs)
        babel.locale = self._get_valid_language(lang_code)
        context_var.set(babel.gettext)
        request.state.babel = babel
        request.state.language = babel.locale
        response = await call_next(request)
        return response

    def _get_valid_language(self, lang_code: Optional[str]) -> str:
        available_locales = {"en", "zh", "fr"}
        if lang_code and lang_code[:2].lower() in available_locales:
            return lang_code[:2]
        return "en"
