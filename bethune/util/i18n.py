import logging
from contextvars import Con<PERSON><PERSON><PERSON>
from typing import Optional, Callable, Any

from fastapi import Request, Response
from starlette.templating import Jinja2Templates
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

from fastapi_babel import Babel, BabelMiddleware
from fastapi_babel.local_context import context_var
from fastapi_babel.properties import RootConfigs

context_var: ContextVar[Babel] = ContextVar("gettext")

_global_jinja_templates: Optional[Jinja2Templates] = None

def set_global_jinja_templates(templates: Jinja2Templates) -> None:
    global _global_jinja_templates
    _global_jinja_templates = templates

    if templates:
        templates.env.globals['_'] = get_text

def get_text(raw_message) -> str:
    """
    获取原始消息的本地化文本
    :param raw_message 原始消息
    :return 本地化消息
    """
    try:
        return context_var.get()(raw_message)
    except LookupError:
        logging.warning(f"Can't find message: {raw_message}")
        return raw_message

class DirectBabelMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, babel_configs: RootConfigs, locale_selector: Callable):
        super().__init__(app)
        self.babel_configs = babel_configs
        self.locale_selector = locale_selector


    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        lang_code: Optional[str] = self.locale_selector(request)
        babel = Babel(configs=self.babel_configs)
        babel.locale = self._get_valid_language(lang_code)
        context_var.set(babel.gettext)
        request.state.babel = babel
        request.state.language = babel.locale
        response = await call_next(request)
        return response

    def _get_valid_language(self, lang_code: Optional[str]) -> str:
        available_locales = {"en", "zh", "fr"}
        if lang_code and lang_code[:2].lower() in available_locales:
            return lang_code[:2]
        return "en"
