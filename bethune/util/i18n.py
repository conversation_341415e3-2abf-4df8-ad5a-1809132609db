import logging
from contextvars import <PERSON><PERSON><PERSON><PERSON>
from typing import Optional, Callable, Any

from fastapi import Request, Response
from starlette.templating import Jinja2Templates
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint, DispatchFunction

from fastapi_babel import Babel, BabelMiddleware
from fastapi_babel.local_context import context_var
from fastapi_babel.properties import RootConfigs
from starlette.types import ASGIApp

context_var: ContextVar[Babel] = ContextVar("gettext")

_global_jinja_templates: Optional[Jinja2Templates] = None

def set_global_jinja_templates(templates: Jinja2Templates) -> None:
    global _global_jinja_templates
    _global_jinja_templates = templates

    if templates:
        templates.env.globals['_'] = get_text

def get_text(raw_message) -> str:
    """
    获取原始消息的本地化文本
    :param raw_message 原始消息
    :return 本地化消息
    """
    try:
        return context_var.get()(raw_message)
    except LookupError:
        logging.warning(f"Can't find message: {raw_message}")
        return raw_message

# class DirectBabelMiddleware(BaseHTTPMiddleware):
#     def __init__(self, app, babel_configs: RootConfigs, locale_selector: Callable):
#         super().__init__(app)
#         self.babel_configs = babel_configs
#         self.locale_selector = locale_selector
#
#     async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
#         lang_code: Optional[str] = self.locale_selector(request)
#         babel = Babel(configs=self.babel_configs)
#         babel.locale = self._get_valid_language(lang_code)
#         context_var.set(babel.gettext)
#         request.state.babel = babel
#         request.state.language = babel.locale
#         response = await call_next(request)
#         return response
#
#     def _get_valid_language(self, lang_code: Optional[str]) -> str:
#         available_locales = {"en", "zh", "fr"}
#         if lang_code and lang_code[:2].lower() in available_locales:
#             return lang_code[:2]
#         return "en"


class DirectBabelMiddleware(BabelMiddleware):
    def __init__(
        self,
        app: ASGIApp,
        babel_configs: RootConfigs,
        locale_selector: Optional[Callable[[Request], Optional[str]]] = None,
        jinja2_templates: Optional[Jinja2Templates] = None,
    ) -> None:
        # ✅ 正确调用父类构造方法
        super().__init__(
            app,
            babel_configs=babel_configs,
            locale_selector=locale_selector,
            jinja2_templates=jinja2_templates,
        )
        self.jinja2_templates = _global_jinja_templates

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        # ✅ 使用父类方法获取语言码
        lang_code = self.get_language(
            self.locale_selector(request) if self.locale_selector else None
        )

        # ✅ 创建当前请求的 Babel 实例
        babel = Babel(configs=self.babel_configs)
        babel.locale = lang_code

        # ✅ 设置上下文变量供模板使用
        context_var.set(babel.gettext)

        # ✅ 保存 babel 实例到 request.state
        request.state.babel = babel
        request.state.language = lang_code

        # ✅ 继续处理请求
        response = await call_next(request)
        return response
