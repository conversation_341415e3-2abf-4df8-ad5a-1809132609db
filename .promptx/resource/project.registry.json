{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-04T02:46:24.526Z", "updatedAt": "2025-08-04T02:46:24.542Z", "resourceCount": 21}, "resources": [{"id": "alex", "source": "project", "protocol": "role", "name": "<PERSON> 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/alex/alex.role.md", "metadata": {"createdAt": "2025-08-04T02:46:24.528Z", "updatedAt": "2025-08-04T02:46:24.528Z", "scannedAt": "2025-08-04T02:46:24.528Z", "path": "role/alex/alex.role.md"}}, {"id": "fastapi-development", "source": "project", "protocol": "execution", "name": "Fastapi Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/alex/execution/fastapi-development.execution.md", "metadata": {"createdAt": "2025-08-04T02:46:24.529Z", "updatedAt": "2025-08-04T02:46:24.529Z", "scannedAt": "2025-08-04T02:46:24.529Z", "path": "role/alex/execution/fastapi-development.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/alex/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T02:46:24.530Z", "updatedAt": "2025-08-04T02:46:24.530Z", "scannedAt": "2025-08-04T02:46:24.530Z", "path": "role/alex/thought/documentation-thinking.thought.md"}}, {"id": "i18n-thinking", "source": "project", "protocol": "thought", "name": "I18n Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/alex/thought/i18n-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T02:46:24.530Z", "updatedAt": "2025-08-04T02:46:24.530Z", "scannedAt": "2025-08-04T02:46:24.530Z", "path": "role/alex/thought/i18n-thinking.thought.md"}}, {"id": "problem-solving", "source": "project", "protocol": "thought", "name": "Problem Solving 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/alex/thought/problem-solving.thought.md", "metadata": {"createdAt": "2025-08-04T02:46:24.530Z", "updatedAt": "2025-08-04T02:46:24.530Z", "scannedAt": "2025-08-04T02:46:24.530Z", "path": "role/alex/thought/problem-solving.thought.md"}}, {"id": "python-senior-thinking", "source": "project", "protocol": "thought", "name": "Python Senior Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/alex/thought/python-senior-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T02:46:24.531Z", "updatedAt": "2025-08-04T02:46:24.531Z", "scannedAt": "2025-08-04T02:46:24.531Z", "path": "role/alex/thought/python-senior-thinking.thought.md"}}, {"id": "research-methodology", "source": "project", "protocol": "execution", "name": "Research Methodology 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/research-methodology.execution.md", "metadata": {"createdAt": "2025-08-04T02:46:24.532Z", "updatedAt": "2025-08-04T02:46:24.532Z", "scannedAt": "2025-08-04T02:46:24.532Z", "path": "role/pepper/execution/research-methodology.execution.md"}}, {"id": "pepper", "source": "project", "protocol": "role", "name": "Pepper 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pepper/pepper.role.md", "metadata": {"createdAt": "2025-08-04T02:46:24.533Z", "updatedAt": "2025-08-04T02:46:24.533Z", "scannedAt": "2025-08-04T02:46:24.533Z", "path": "role/pepper/pepper.role.md"}}, {"id": "deep-research", "source": "project", "protocol": "thought", "name": "Deep Research 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/deep-research.thought.md", "metadata": {"createdAt": "2025-08-04T02:46:24.533Z", "updatedAt": "2025-08-04T02:46:24.533Z", "scannedAt": "2025-08-04T02:46:24.533Z", "path": "role/pepper/thought/deep-research.thought.md"}}, {"id": "python-development-workflow", "source": "project", "protocol": "execution", "name": "Python Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-backend-expert/execution/python-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-04T02:46:24.534Z", "updatedAt": "2025-08-04T02:46:24.534Z", "scannedAt": "2025-08-04T02:46:24.534Z", "path": "role/python-backend-expert/execution/python-development-workflow.execution.md"}}, {"id": "python-backend-stack", "source": "project", "protocol": "knowledge", "name": "Python Backend Stack 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/python-backend-expert/knowledge/python-backend-stack.knowledge.md", "metadata": {"createdAt": "2025-08-04T02:46:24.535Z", "updatedAt": "2025-08-04T02:46:24.535Z", "scannedAt": "2025-08-04T02:46:24.535Z", "path": "role/python-backend-expert/knowledge/python-backend-stack.knowledge.md"}}, {"id": "python-backend-expert", "source": "project", "protocol": "role", "name": "Python Backend Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/python-backend-expert/python-backend-expert.role.md", "metadata": {"createdAt": "2025-08-04T02:46:24.536Z", "updatedAt": "2025-08-04T02:46:24.536Z", "scannedAt": "2025-08-04T02:46:24.536Z", "path": "role/python-backend-expert/python-backend-expert.role.md"}}, {"id": "python-expertise", "source": "project", "protocol": "thought", "name": "Python Expertise 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/python-backend-expert/thought/python-expertise.thought.md", "metadata": {"createdAt": "2025-08-04T02:46:24.536Z", "updatedAt": "2025-08-04T02:46:24.536Z", "scannedAt": "2025-08-04T02:46:24.536Z", "path": "role/python-backend-expert/thought/python-expertise.thought.md"}}, {"id": "bug-fixer", "source": "project", "protocol": "manual", "name": "Bug Fixer manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/bug-fixer/bug-fixer.manual.md", "metadata": {"createdAt": "2025-08-04T02:46:24.537Z", "updatedAt": "2025-08-04T02:46:24.537Z", "scannedAt": "2025-08-04T02:46:24.537Z", "path": "tool/bug-fixer/bug-fixer.manual.md"}}, {"id": "bug-fixer", "source": "project", "protocol": "tool", "name": "Bug Fixer tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/bug-fixer/bug-fixer.tool.js", "metadata": {"createdAt": "2025-08-04T02:46:24.538Z", "updatedAt": "2025-08-04T02:46:24.538Z", "scannedAt": "2025-08-04T02:46:24.538Z", "path": "tool/bug-fixer/bug-fixer.tool.js"}}, {"id": "crud-generator", "source": "project", "protocol": "manual", "name": "Crud Generator manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/crud-generator/crud-generator.manual.md", "metadata": {"createdAt": "2025-08-04T02:46:24.539Z", "updatedAt": "2025-08-04T02:46:24.539Z", "scannedAt": "2025-08-04T02:46:24.539Z", "path": "tool/crud-generator/crud-generator.manual.md"}}, {"id": "crud-generator", "source": "project", "protocol": "tool", "name": "Crud Generator tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/crud-generator/crud-generator.tool.js", "metadata": {"createdAt": "2025-08-04T02:46:24.539Z", "updatedAt": "2025-08-04T02:46:24.539Z", "scannedAt": "2025-08-04T02:46:24.539Z", "path": "tool/crud-generator/crud-generator.tool.js"}}, {"id": "sql-generator", "source": "project", "protocol": "manual", "name": "Sql Generator manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/sql-generator/sql-generator.manual.md", "metadata": {"createdAt": "2025-08-04T02:46:24.540Z", "updatedAt": "2025-08-04T02:46:24.540Z", "scannedAt": "2025-08-04T02:46:24.540Z", "path": "tool/sql-generator/sql-generator.manual.md"}}, {"id": "sql-generator", "source": "project", "protocol": "tool", "name": "Sql Generator tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/sql-generator/sql-generator.tool.js", "metadata": {"createdAt": "2025-08-04T02:46:24.541Z", "updatedAt": "2025-08-04T02:46:24.541Z", "scannedAt": "2025-08-04T02:46:24.541Z", "path": "tool/sql-generator/sql-generator.tool.js"}}, {"id": "test-generator", "source": "project", "protocol": "manual", "name": "Test Generator manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/test-generator/test-generator.manual.md", "metadata": {"createdAt": "2025-08-04T02:46:24.541Z", "updatedAt": "2025-08-04T02:46:24.541Z", "scannedAt": "2025-08-04T02:46:24.541Z", "path": "tool/test-generator/test-generator.manual.md"}}, {"id": "test-generator", "source": "project", "protocol": "tool", "name": "Test Generator tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/test-generator/test-generator.tool.js", "metadata": {"createdAt": "2025-08-04T02:46:24.542Z", "updatedAt": "2025-08-04T02:46:24.542Z", "scannedAt": "2025-08-04T02:46:24.542Z", "path": "tool/test-generator/test-generator.tool.js"}}], "stats": {"totalResources": 21, "byProtocol": {"role": 3, "execution": 3, "thought": 6, "knowledge": 1, "manual": 4, "tool": 4}, "bySource": {"project": 21}}}